import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';

// Import des pages
import Login from './pages/Login';
import TechnicianeDashbor from './pages/TechnicianeDashbor';
import Clients from './pages/Clients';
import Consommation from './pages/Consommation';
import Factures from './pages/Factures';
import QRScanner from './pages/QRScanner';
import CarteClients from './pages/CarteClients';
import Historique from './pages/Historique';

const AppRouter = () => {
  return (
    <Router>
      <Routes>
        {/* Route par défaut - redirection vers login */}
        <Route path="/" element={<Navigate to="/login" replace />} />
        
        {/* Page de connexion */}
        <Route path="/login" element={<Login />} />
        
        {/* Dashboard technicien */}
        <Route path="/dashboard-technicien" element={<TechnicianeDashbor />} />
        
        {/* Pages des fonctionnalités technicien */}
        <Route path="/clients" element={<Clients />} />
        <Route path="/consommation" element={<Consommation />} />
        <Route path="/factures" element={<Factures />} />
        <Route path="/qr-scanner" element={<QRScanner />} />
        <Route path="/carte-clients" element={<CarteClients />} />
        <Route path="/historique" element={<Historique />} />
        
        {/* Route 404 - redirection vers login */}
        <Route path="*" element={<Navigate to="/login" replace />} />
      </Routes>
    </Router>
  );
};

export default AppRouter;
