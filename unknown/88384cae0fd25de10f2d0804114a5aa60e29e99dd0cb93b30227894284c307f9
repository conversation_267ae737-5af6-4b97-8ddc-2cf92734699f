import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  StatusBar,
} from 'react-native';

const TestClients = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    testAPI();
  }, []);

  const testAPI = async () => {
    try {
      console.log('🔍 Test de l\'API clients...');
      
      const response = await fetch('http://localhost:4000/api/clients');
      console.log('📡 Réponse HTTP:', response.status, response.statusText);
      
      if (response.ok) {
        const data = await response.json();
        console.log('📊 Données brutes reçues:', data);
        console.log('📊 Type de données:', typeof data);
        console.log('📊 Est un tableau?', Array.isArray(data));
        
        if (data.clients) {
          console.log('📊 Clients trouvés:', data.clients.length);
          console.log('📊 Premier client:', data.clients[0]);
        }
        
        setData(data);
      } else {
        const errorText = await response.text();
        console.error('❌ Erreur HTTP:', response.status, errorText);
        setError(`Erreur HTTP ${response.status}: ${errorText}`);
      }
    } catch (err) {
      console.error('❌ Erreur de connexion:', err);
      setError(`Erreur de connexion: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" />
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Chargement...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />
      <ScrollView style={styles.scrollView}>
        <Text style={styles.title}>🧪 Test API Clients</Text>

        {error && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorTitle}>Erreur:</Text>
            <Text style={styles.errorText}>{error}</Text>
          </View>
        )}

        {data && (
          <View style={styles.dataContainer}>
            <Text style={styles.sectionTitle}>📊 Données reçues:</Text>
            <View style={styles.jsonContainer}>
              <Text style={styles.jsonText}>{JSON.stringify(data, null, 2)}</Text>
            </View>

            {data.clients && (
              <View style={styles.clientsContainer}>
                <Text style={styles.sectionTitle}>👥 Liste des clients ({data.clients.length}):</Text>
                {data.clients.map((client, index) => (
                  <View key={index} style={styles.clientItem}>
                    <Text style={styles.clientText}>
                      <Text style={styles.clientName}>{client.nom} {client.prenom}</Text>
                      {' - '}{client.ville} ({client.statut})
                    </Text>
                  </View>
                ))}
              </View>
            )}
          </View>
        )}

        <TouchableOpacity style={styles.retryButton} onPress={testAPI}>
          <Text style={styles.retryButtonText}>🔄 Retester l'API</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  scrollView: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 20,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#6b7280',
  },
  errorContainer: {
    backgroundColor: '#ffe6e6',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#fecaca',
  },
  errorTitle: {
    fontWeight: 'bold',
    color: '#ef4444',
    fontSize: 16,
    marginBottom: 5,
  },
  errorText: {
    color: '#ef4444',
    fontSize: 14,
  },
  dataContainer: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 10,
  },
  jsonContainer: {
    backgroundColor: '#f5f5f5',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  jsonText: {
    fontSize: 12,
    color: '#374151',
    fontFamily: 'monospace',
  },
  clientsContainer: {
    marginTop: 10,
  },
  clientItem: {
    backgroundColor: 'white',
    padding: 10,
    borderRadius: 6,
    marginBottom: 5,
    borderLeftWidth: 3,
    borderLeftColor: '#6366f1',
  },
  clientText: {
    fontSize: 14,
    color: '#374151',
  },
  clientName: {
    fontWeight: 'bold',
    color: '#1f2937',
  },
  retryButton: {
    backgroundColor: '#6366f1',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 20,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default TestClients;
