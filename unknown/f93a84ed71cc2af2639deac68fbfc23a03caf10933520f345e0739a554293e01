import React, { useState, useEffect } from 'react';

const Factures = () => {
  const [factures, setFactures] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filter, setFilter] = useState('all'); // all, payee, non_payee

  useEffect(() => {
    fetchFactures();
  }, []);

  const fetchFactures = async () => {
    try {
      const response = await fetch('http://localhost:4000/api/factures');
      if (response.ok) {
        const data = await response.json();
        setFactures(data);
      } else {
        setError('Erreur lors du chargement des factures');
      }
    } catch (err) {
      setError('Erreur de connexion au serveur');
    } finally {
      setLoading(false);
    }
  };

  const filteredFactures = factures.filter(facture => {
    if (filter === 'payee') return facture.status === 'payée';
    if (filter === 'non_payee') return facture.status === 'non payée';
    return true;
  });

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  const formatMontant = (montant) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'MAD'
    }).format(montant);
  };

  const downloadPDF = async (factureId) => {
    try {
      const response = await fetch(`http://localhost:4000/api/factures/${factureId}/pdf`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `facture_${factureId}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (err) {
      console.error('Erreur lors du téléchargement:', err);
    }
  };

  const goBack = () => {
    window.history.back();
  };

  if (loading) {
    return (
      <div className="page-container">
        <div className="loading">Chargement des factures...</div>
      </div>
    );
  }

  return (
    <div className="page-container">
      <header className="page-header">
        <button onClick={goBack} className="back-btn">← Retour</button>
        <h1>🧾 Mes Factures</h1>
      </header>

      <main className="page-content">
        {error && <div className="error-message">{error}</div>}
        
        <div className="filters">
          <button 
            className={`filter-btn ${filter === 'all' ? 'active' : ''}`}
            onClick={() => setFilter('all')}
          >
            Toutes ({factures.length})
          </button>
          <button 
            className={`filter-btn ${filter === 'payee' ? 'active' : ''}`}
            onClick={() => setFilter('payee')}
          >
            Payées ({factures.filter(f => f.status === 'payée').length})
          </button>
          <button 
            className={`filter-btn ${filter === 'non_payee' ? 'active' : ''}`}
            onClick={() => setFilter('non_payee')}
          >
            Non Payées ({factures.filter(f => f.status === 'non payée').length})
          </button>
        </div>

        <div className="factures-grid">
          {filteredFactures.length > 0 ? (
            filteredFactures.map(facture => (
              <div key={facture.idfact} className="facture-card">
                <div className="facture-header">
                  <h3>Facture #{facture.reference}</h3>
                  <span className={`status ${facture.status === 'payée' ? 'paid' : 'unpaid'}`}>
                    {facture.status}
                  </span>
                </div>
                
                <div className="facture-info">
                  <p><strong>Date:</strong> {formatDate(facture.date)}</p>
                  <p><strong>Période:</strong> {facture.periode}</p>
                  <p><strong>Montant:</strong> {formatMontant(facture.montant)}</p>
                </div>

                <div className="facture-actions">
                  <button 
                    className="action-btn primary"
                    onClick={() => downloadPDF(facture.idfact)}
                  >
                    📄 Télécharger PDF
                  </button>
                  <button className="action-btn secondary">
                    👁️ Voir Détails
                  </button>
                </div>
              </div>
            ))
          ) : (
            <div className="no-data">
              <p>Aucune facture trouvée</p>
            </div>
          )}
        </div>
      </main>

      <style jsx>{`
        .page-container {
          min-height: 100vh;
          background-color: #f8fafc;
          padding: 20px;
        }
        .page-header {
          display: flex;
          align-items: center;
          gap: 20px;
          margin-bottom: 30px;
          background: white;
          padding: 20px;
          border-radius: 12px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .back-btn {
          background: #6366f1;
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 8px;
          cursor: pointer;
        }
        .filters {
          display: flex;
          gap: 10px;
          margin-bottom: 30px;
          justify-content: center;
        }
        .filter-btn {
          padding: 10px 20px;
          border: 2px solid #e5e7eb;
          background: white;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.2s;
        }
        .filter-btn.active {
          background: #3b82f6;
          color: white;
          border-color: #3b82f6;
        }
        .factures-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
          gap: 20px;
        }
        .facture-card {
          background: white;
          border-radius: 12px;
          padding: 20px;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .facture-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 15px;
        }
        .facture-header h3 {
          color: #1f2937;
          margin: 0;
        }
        .status {
          padding: 4px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 500;
        }
        .status.paid {
          background: #d1fae5;
          color: #065f46;
        }
        .status.unpaid {
          background: #fee2e2;
          color: #991b1b;
        }
        .facture-info p {
          margin: 8px 0;
          color: #6b7280;
        }
        .facture-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
        }
        .action-btn {
          flex: 1;
          padding: 10px;
          border: none;
          border-radius: 8px;
          cursor: pointer;
          font-size: 14px;
        }
        .action-btn.primary {
          background: #ef4444;
          color: white;
        }
        .action-btn.secondary {
          background: #6b7280;
          color: white;
        }
        .loading, .error-message, .no-data {
          text-align: center;
          padding: 40px;
          background: white;
          border-radius: 12px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .error-message {
          color: #ef4444;
          background: #fef2f2;
          border: 1px solid #fecaca;
        }
      `}</style>
    </div>
  );
};

export default Factures;
