import React, { useState, useEffect } from 'react';

const Consommation = () => {
  const [formData, setFormData] = useState({
    periode: '',
    idClient: '',
    idContract: '',
    consommationActuelle: '',
    consommationPre: '',
    jours: ''
  });
  const [clients, setClients] = useState([]);
  const [contracts, setContracts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  useEffect(() => {
    fetchClients();
  }, []);

  const fetchClients = async () => {
    try {
      const response = await fetch('http://localhost:4000/api/clients');
      if (response.ok) {
        const data = await response.json();
        setClients(data);
      }
    } catch (err) {
      console.error('Erreur lors du chargement des clients:', err);
    }
  };

  const fetchContracts = async (clientId) => {
    try {
      const response = await fetch(`http://localhost:4000/api/contracts/client/${clientId}`);
      if (response.ok) {
        const data = await response.json();
        setContracts(data);
        if (data.length === 1) {
          setFormData(prev => ({ ...prev, idContract: data[0].idcontract }));
        }
      }
    } catch (err) {
      console.error('Erreur lors du chargement des contrats:', err);
    }
  };

  const handleClientChange = (e) => {
    const clientId = e.target.value;
    setFormData(prev => ({ ...prev, idClient: clientId, idContract: '' }));
    if (clientId) {
      fetchContracts(clientId);
    } else {
      setContracts([]);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');

    // Validation
    if (parseFloat(formData.consommationActuelle) <= parseFloat(formData.consommationPre)) {
      setMessage('La consommation actuelle doit être supérieure à la consommation précédente');
      setLoading(false);
      return;
    }

    try {
      const response = await fetch('http://localhost:4000/api/consommation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          idTech: 1, // ID du technicien connecté
          status: 'En cours'
        }),
      });

      if (response.ok) {
        setMessage('Consommation enregistrée avec succès!');
        setFormData({
          periode: '',
          idClient: '',
          idContract: '',
          consommationActuelle: '',
          consommationPre: '',
          jours: ''
        });
        setContracts([]);
      } else {
        setMessage('Erreur lors de l\'enregistrement');
      }
    } catch (err) {
      setMessage('Erreur de connexion au serveur');
    } finally {
      setLoading(false);
    }
  };

  const goBack = () => {
    window.history.back();
  };

  return (
    <div className="page-container">
      <header className="page-header">
        <button onClick={goBack} className="back-btn">← Retour</button>
        <h1>💧 Saisie Consommation</h1>
      </header>

      <main className="page-content">
        <div className="form-container">
          <form onSubmit={handleSubmit} className="consumption-form">
            <div className="form-group">
              <label>Période (YYYY-MM)</label>
              <input
                type="month"
                value={formData.periode}
                onChange={(e) => setFormData(prev => ({ ...prev, periode: e.target.value }))}
                required
              />
            </div>

            <div className="form-group">
              <label>Client</label>
              <select
                value={formData.idClient}
                onChange={handleClientChange}
                required
              >
                <option value="">Sélectionner un client</option>
                {clients.map(client => (
                  <option key={client.idclient} value={client.idclient}>
                    {client.nom} {client.prenom}
                  </option>
                ))}
              </select>
            </div>

            {contracts.length > 0 && (
              <div className="form-group">
                <label>Contrat</label>
                {contracts.length === 1 ? (
                  <input
                    type="text"
                    value={`Contrat ${contracts[0].idcontract} - ${contracts[0].marquecompteur}`}
                    readOnly
                  />
                ) : (
                  <select
                    value={formData.idContract}
                    onChange={(e) => setFormData(prev => ({ ...prev, idContract: e.target.value }))}
                    required
                  >
                    <option value="">Sélectionner un contrat</option>
                    {contracts.map(contract => (
                      <option key={contract.idcontract} value={contract.idcontract}>
                        Contrat {contract.idcontract} - {contract.marquecompteur}
                      </option>
                    ))}
                  </select>
                )}
              </div>
            )}

            <div className="form-group">
              <label>Consommation Précédente (m³)</label>
              <input
                type="number"
                step="0.01"
                value={formData.consommationPre}
                onChange={(e) => setFormData(prev => ({ ...prev, consommationPre: e.target.value }))}
                required
              />
            </div>

            <div className="form-group">
              <label>Consommation Actuelle (m³)</label>
              <input
                type="number"
                step="0.01"
                value={formData.consommationActuelle}
                onChange={(e) => setFormData(prev => ({ ...prev, consommationActuelle: e.target.value }))}
                required
              />
            </div>

            <div className="form-group">
              <label>Nombre de jours</label>
              <input
                type="number"
                value={formData.jours}
                onChange={(e) => setFormData(prev => ({ ...prev, jours: e.target.value }))}
                required
              />
            </div>

            {message && (
              <div className={`message ${message.includes('succès') ? 'success' : 'error'}`}>
                {message}
              </div>
            )}

            <button type="submit" disabled={loading} className="submit-btn">
              {loading ? 'Enregistrement...' : 'Enregistrer la Consommation'}
            </button>
          </form>
        </div>
      </main>

      <style jsx>{`
        .page-container {
          min-height: 100vh;
          background-color: #f8fafc;
          padding: 20px;
        }
        .page-header {
          display: flex;
          align-items: center;
          gap: 20px;
          margin-bottom: 30px;
          background: white;
          padding: 20px;
          border-radius: 12px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .back-btn {
          background: #6366f1;
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 8px;
          cursor: pointer;
        }
        .form-container {
          max-width: 600px;
          margin: 0 auto;
          background: white;
          padding: 30px;
          border-radius: 12px;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .form-group {
          margin-bottom: 20px;
        }
        .form-group label {
          display: block;
          margin-bottom: 8px;
          font-weight: 500;
          color: #374151;
        }
        .form-group input,
        .form-group select {
          width: 100%;
          padding: 12px;
          border: 1px solid #d1d5db;
          border-radius: 8px;
          font-size: 16px;
        }
        .submit-btn {
          width: 100%;
          background: #10b981;
          color: white;
          border: none;
          padding: 15px;
          border-radius: 8px;
          font-size: 16px;
          cursor: pointer;
        }
        .submit-btn:disabled {
          background: #9ca3af;
          cursor: not-allowed;
        }
        .message {
          padding: 12px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        .message.success {
          background: #d1fae5;
          color: #065f46;
          border: 1px solid #a7f3d0;
        }
        .message.error {
          background: #fee2e2;
          color: #991b1b;
          border: 1px solid #fecaca;
        }
      `}</style>
    </div>
  );
};

export default Consommation;
