import React, { useState, useRef, useEffect } from 'react';

const QRScanner = () => {
  const [isScanning, setIsScanning] = useState(false);
  const [scannedData, setScannedData] = useState('');
  const [clientInfo, setClientInfo] = useState(null);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const videoRef = useRef(null);
  const streamRef = useRef(null);

  const startScanning = async () => {
    try {
      setError('');
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: 'environment' }
      });
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        streamRef.current = stream;
        setIsScanning(true);
      }
    } catch (err) {
      setError('Impossible d\'accéder à la caméra. Vérifiez les permissions.');
    }
  };

  const stopScanning = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    setIsScanning(false);
  };

  const handleManualInput = async () => {
    const qrCode = prompt('Entrez le code QR manuellement:');
    if (qrCode) {
      await searchClient(qrCode);
    }
  };

  const searchClient = async (qrCode) => {
    setLoading(true);
    setError('');
    setClientInfo(null);

    try {
      const response = await fetch(`http://localhost:4000/api/clients/qr/${qrCode}`);
      
      if (response.ok) {
        const data = await response.json();
        setClientInfo(data);
        setScannedData(qrCode);
      } else if (response.status === 404) {
        setError('Ce QR code ne correspond à aucun client dans notre base de données.');
      } else {
        setError('Erreur lors de la recherche du client.');
      }
    } catch (err) {
      setError('Erreur de connexion au serveur.');
    } finally {
      setLoading(false);
    }
  };

  const goBack = () => {
    stopScanning();
    window.history.back();
  };

  const resetScanner = () => {
    setScannedData('');
    setClientInfo(null);
    setError('');
  };

  useEffect(() => {
    return () => {
      stopScanning();
    };
  }, []);

  return (
    <div className="page-container">
      <header className="page-header">
        <button onClick={goBack} className="back-btn">← Retour</button>
        <h1>📱 Scanner QR</h1>
      </header>

      <main className="page-content">
        <div className="scanner-container">
          {!isScanning && !clientInfo && (
            <div className="scanner-start">
              <div className="scanner-icon">📷</div>
              <h2>Scanner un code QR</h2>
              <p>Pointez votre caméra vers le code QR du compteur</p>
              
              <div className="scanner-actions">
                <button onClick={startScanning} className="start-btn">
                  🎥 Démarrer le scan
                </button>
                <button onClick={handleManualInput} className="manual-btn">
                  ⌨️ Saisie manuelle
                </button>
              </div>
            </div>
          )}

          {isScanning && (
            <div className="scanner-active">
              <div className="camera-container">
                <video
                  ref={videoRef}
                  autoPlay
                  playsInline
                  className="camera-video"
                />
                <div className="scanner-overlay">
                  <div className="scanner-frame"></div>
                </div>
              </div>
              
              <div className="scanner-controls">
                <button onClick={stopScanning} className="stop-btn">
                  ⏹️ Arrêter le scan
                </button>
                <button onClick={handleManualInput} className="manual-btn">
                  ⌨️ Saisie manuelle
                </button>
              </div>
              
              <p className="scanner-instruction">
                Placez le code QR dans le cadre pour le scanner
              </p>
            </div>
          )}

          {loading && (
            <div className="loading-container">
              <div className="loading-spinner"></div>
              <p>Recherche du client...</p>
            </div>
          )}

          {error && (
            <div className="error-container">
              <div className="error-icon">❌</div>
              <p className="error-message">{error}</p>
              <button onClick={resetScanner} className="retry-btn">
                🔄 Réessayer
              </button>
            </div>
          )}

          {clientInfo && (
            <div className="client-result">
              <div className="success-icon">✅</div>
              <h2>Client trouvé!</h2>
              
              <div className="client-card">
                <h3>{clientInfo.nom} {clientInfo.prenom}</h3>
                <div className="client-details">
                  <p><strong>Adresse:</strong> {clientInfo.adresse}</p>
                  <p><strong>Ville:</strong> {clientInfo.ville}</p>
                  <p><strong>Téléphone:</strong> {clientInfo.tel}</p>
                  <p><strong>Email:</strong> {clientInfo.email}</p>
                  <p><strong>Code QR:</strong> {scannedData}</p>
                </div>
              </div>

              <div className="client-actions">
                <button className="action-btn primary">
                  💧 Nouveau Relevé
                </button>
                <button className="action-btn secondary">
                  📋 Voir Détails
                </button>
                <button onClick={resetScanner} className="action-btn tertiary">
                  🔄 Scanner Autre
                </button>
              </div>
            </div>
          )}
        </div>
      </main>

      <style jsx>{`
        .page-container {
          min-height: 100vh;
          background-color: #f8fafc;
          padding: 20px;
        }
        .page-header {
          display: flex;
          align-items: center;
          gap: 20px;
          margin-bottom: 30px;
          background: white;
          padding: 20px;
          border-radius: 12px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .back-btn {
          background: #6366f1;
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 8px;
          cursor: pointer;
        }
        .scanner-container {
          max-width: 500px;
          margin: 0 auto;
          background: white;
          border-radius: 12px;
          padding: 30px;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
          text-align: center;
        }
        .scanner-start {
          padding: 40px 20px;
        }
        .scanner-icon {
          font-size: 64px;
          margin-bottom: 20px;
        }
        .scanner-actions {
          display: flex;
          flex-direction: column;
          gap: 15px;
          margin-top: 30px;
        }
        .start-btn {
          background: #10b981;
          color: white;
          border: none;
          padding: 15px 30px;
          border-radius: 8px;
          font-size: 16px;
          cursor: pointer;
        }
        .manual-btn {
          background: #6b7280;
          color: white;
          border: none;
          padding: 12px 30px;
          border-radius: 8px;
          cursor: pointer;
        }
        .camera-container {
          position: relative;
          margin-bottom: 20px;
        }
        .camera-video {
          width: 100%;
          max-width: 400px;
          border-radius: 8px;
        }
        .scanner-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .scanner-frame {
          width: 200px;
          height: 200px;
          border: 3px solid #10b981;
          border-radius: 8px;
          background: transparent;
        }
        .scanner-controls {
          display: flex;
          gap: 15px;
          justify-content: center;
          margin-bottom: 20px;
        }
        .stop-btn {
          background: #ef4444;
          color: white;
          border: none;
          padding: 12px 20px;
          border-radius: 8px;
          cursor: pointer;
        }
        .client-result {
          padding: 20px;
        }
        .success-icon {
          font-size: 48px;
          margin-bottom: 20px;
        }
        .client-card {
          background: #f9fafb;
          border-radius: 8px;
          padding: 20px;
          margin: 20px 0;
          text-align: left;
        }
        .client-actions {
          display: flex;
          flex-direction: column;
          gap: 10px;
          margin-top: 20px;
        }
        .action-btn {
          padding: 12px 20px;
          border: none;
          border-radius: 8px;
          cursor: pointer;
          font-size: 14px;
        }
        .action-btn.primary {
          background: #10b981;
          color: white;
        }
        .action-btn.secondary {
          background: #3b82f6;
          color: white;
        }
        .action-btn.tertiary {
          background: #6b7280;
          color: white;
        }
        .error-container {
          padding: 40px 20px;
        }
        .error-icon {
          font-size: 48px;
          margin-bottom: 20px;
        }
        .error-message {
          color: #ef4444;
          margin-bottom: 20px;
        }
        .retry-btn {
          background: #6b7280;
          color: white;
          border: none;
          padding: 12px 20px;
          border-radius: 8px;
          cursor: pointer;
        }
        .loading-container {
          padding: 40px 20px;
        }
        .loading-spinner {
          width: 40px;
          height: 40px;
          border: 4px solid #e5e7eb;
          border-top: 4px solid #3b82f6;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 20px;
        }
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default QRScanner;
