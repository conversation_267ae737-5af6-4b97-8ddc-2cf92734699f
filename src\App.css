/* Styles pour la version web d'AquaTrack */

/* Page de connexion */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-card {
  background: white;
  border-radius: 15px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  width: 100%;
}

.logo {
  text-align: center;
  margin-bottom: 30px;
}

.logo-icon {
  font-size: 60px;
  margin-bottom: 10px;
}

.logo h1 {
  color: #2196F3;
  font-size: 32px;
  font-weight: bold;
  margin: 0 0 8px 0;
}

.logo p {
  color: #666;
  font-size: 16px;
  margin: 0;
}

.login-form {
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #333;
  font-weight: 600;
  font-size: 16px;
}

.form-group input {
  width: 100%;
  border: 2px solid #ddd;
  border-radius: 8px;
  padding: 15px;
  font-size: 16px;
  background: #fff;
  box-sizing: border-box;
}

.form-group input:focus {
  outline: none;
  border-color: #2196F3;
}

.login-btn {
  width: 100%;
  background: #2196F3;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 15px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s;
}

.login-btn:hover {
  background: #1976d2;
}

.login-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.test-info {
  background: #e3f2fd;
  padding: 15px;
  border-radius: 10px;
  border-left: 4px solid #2196F3;
}

.test-info h4 {
  color: #1976d2;
  margin: 0 0 5px 0;
  font-size: 14px;
}

.test-info p {
  color: #1976d2;
  margin: 0;
  font-size: 12px;
  font-family: monospace;
}

/* Dashboard */
.dashboard {
  min-height: 100vh;
  background: #f8fafc;
}

.dashboard-header {
  background: #3b82f6;
  color: white;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dashboard-header h1 {
  margin: 0;
  font-size: 24px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.logout-btn {
  background: #dc2626;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  cursor: pointer;
  font-weight: 600;
}

.logout-btn:hover {
  background: #b91c1c;
}

.dashboard-content {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-card {
  background: white;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 30px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.welcome-card h2 {
  color: #1f2937;
  margin: 0 0 10px 0;
  font-size: 28px;
}

.welcome-card p {
  color: #6b7280;
  margin: 0;
  font-size: 18px;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 30px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.dashboard-card {
  background: white;
  padding: 35px 25px;
  border-radius: 15px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
  text-align: center;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  min-height: 140px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.card-icon {
  font-size: 52px;
  margin-bottom: 18px;
}

.dashboard-card h3 {
  color: #1f2937;
  margin: 0 0 12px 0;
  font-size: 22px;
  font-weight: 600;
}

.dashboard-card p {
  color: #6b7280;
  margin: 0;
  font-size: 16px;
  line-height: 1.4;
  text-align: center;
}

.info-message {
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 12px;
  padding: 20px;
  margin-top: 30px;
}

.info-message h3 {
  color: #92400e;
  margin: 0 0 15px 0;
}

.info-message p {
  color: #92400e;
  margin: 0 0 10px 0;
  line-height: 1.6;
}

.info-message ul {
  color: #92400e;
  margin: 10px 0 0 20px;
}

.info-message code {
  background: #fbbf24;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: monospace;
}

/* Styles pour les statistiques */
.stats-container {
  margin-top: 30px;
}

.stats-container h3 {
  color: #1f2937;
  margin-bottom: 20px;
  font-size: 20px;
  font-weight: bold;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #3b82f6;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

/* Styles pour l'activité récente */
.recent-activity {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-top: 20px;
}

.recent-activity h3 {
  color: #1f2937;
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: bold;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #f9fafb;
  border-radius: 8px;
}

.activity-icon {
  font-size: 24px;
  margin-right: 12px;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.activity-time {
  font-size: 12px;
  color: #6b7280;
}

/* Styles optimisés pour mobile */
@media (max-width: 768px) {
  /* Header mobile */
  .dashboard-header {
    padding: 15px;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .dashboard-header h1 {
    font-size: 20px;
    margin: 0;
  }

  .user-info {
    width: 100%;
    justify-content: space-between;
    font-size: 14px;
  }

  /* Content mobile */
  .dashboard-content {
    padding: 15px;
  }

  /* Welcome card mobile */
  .welcome-card {
    padding: 20px;
    margin-bottom: 20px;
  }

  .welcome-card h2 {
    font-size: 22px;
    margin-bottom: 8px;
  }

  .welcome-card p {
    font-size: 16px;
  }

  /* Dashboard grid mobile */
  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: 15px;
    margin-bottom: 20px;
  }

  .dashboard-card {
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #3b82f6;
  }

  .dashboard-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  }

  .card-icon {
    font-size: 40px;
    margin-bottom: 12px;
  }

  .dashboard-card h3 {
    font-size: 18px;
    margin-bottom: 8px;
    color: #1f2937;
    font-weight: 600;
  }

  .dashboard-card p {
    font-size: 14px;
    color: #6b7280;
    line-height: 1.4;
  }

  /* Stats mobile */
  .stats-container h3 {
    font-size: 18px;
    margin-bottom: 15px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .stat-card {
    padding: 15px;
    border-radius: 12px;
  }

  .stat-number {
    font-size: 24px;
    margin-bottom: 6px;
  }

  .stat-label {
    font-size: 12px;
  }

  /* Activity mobile */
  .recent-activity {
    padding: 15px;
    margin-top: 15px;
  }

  .recent-activity h3 {
    font-size: 16px;
    margin-bottom: 15px;
  }

  .activity-item {
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 10px;
  }

  .activity-icon {
    font-size: 20px;
    margin-right: 10px;
  }

  .activity-title {
    font-size: 14px;
    margin-bottom: 3px;
  }

  .activity-time {
    font-size: 11px;
  }

  /* Logout button mobile */
  .logout-btn {
    padding: 8px 12px;
    font-size: 14px;
    border-radius: 6px;
  }
}

/* Styles pour très petits écrans */
@media (max-width: 480px) {
  .dashboard-header h1 {
    font-size: 18px;
  }

  .user-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .dashboard-content {
    padding: 12px;
  }

  .welcome-card {
    padding: 15px;
  }

  .welcome-card h2 {
    font-size: 20px;
  }

  .dashboard-card {
    padding: 15px;
  }

  .card-icon {
    font-size: 35px;
  }

  .dashboard-card h3 {
    font-size: 16px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }
}

/* Styles pour l'interaction tactile mobile */
.dashboard-card {
  transition: all 0.3s ease;
  cursor: pointer;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.dashboard-card:active {
  transform: scale(0.98);
  background-color: #f8fafc;
}

.logout-btn {
  transition: all 0.2s ease;
  -webkit-tap-highlight-color: transparent;
}

.logout-btn:active {
  transform: scale(0.95);
  background-color: #b91c1c;
}

/* Amélioration de la lisibilité mobile */
@media (max-width: 768px) {
  body {
    font-size: 16px; /* Évite le zoom automatique sur iOS */
  }

  .dashboard {
    min-height: 100vh;
    background: #f8fafc;
  }

  /* Espacement optimisé pour le pouce */
  .dashboard-card {
    min-height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
  }

  /* Couleurs adaptées pour mobile */
  .dashboard-card:nth-child(1) { border-left-color: #3b82f6; }
  .dashboard-card:nth-child(2) { border-left-color: #10b981; }
  .dashboard-card:nth-child(3) { border-left-color: #f59e0b; }
  .dashboard-card:nth-child(4) { border-left-color: #8b5cf6; }
  .dashboard-card:nth-child(5) { border-left-color: #ef4444; }
  .dashboard-card:nth-child(6) { border-left-color: #06b6d4; }
}

/* Animation de chargement pour mobile */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dashboard-card {
  animation: fadeInUp 0.5s ease forwards;
}

.dashboard-card:nth-child(1) { animation-delay: 0.1s; }
.dashboard-card:nth-child(2) { animation-delay: 0.2s; }
.dashboard-card:nth-child(3) { animation-delay: 0.3s; }
.dashboard-card:nth-child(4) { animation-delay: 0.4s; }
.dashboard-card:nth-child(5) { animation-delay: 0.5s; }
.dashboard-card:nth-child(6) { animation-delay: 0.6s; }
