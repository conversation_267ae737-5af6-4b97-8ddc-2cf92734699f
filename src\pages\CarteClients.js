import React, { useState, useEffect } from 'react';

const CarteClients = () => {
  const [secteurs, setSecteurs] = useState([]);
  const [selectedSecteur, setSelectedSecteur] = useState('');
  const [clients, setClients] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [mapLoaded, setMapLoaded] = useState(false);

  useEffect(() => {
    fetchSecteurs();
    loadGoogleMaps();
  }, []);

  const fetchSecteurs = async () => {
    try {
      const response = await fetch('http://localhost:4000/api/secteurs');
      if (response.ok) {
        const data = await response.json();
        setSecteurs(data);
      }
    } catch (err) {
      console.error('Erreur lors du chargement des secteurs:', err);
    }
  };

  const fetchClientsBySecteur = async (secteurId) => {
    setLoading(true);
    setError('');
    
    try {
      const response = await fetch(`http://localhost:4000/api/clients/secteur/${secteurId}`);
      if (response.ok) {
        const data = await response.json();
        setClients(data);
        if (mapLoaded) {
          displayClientsOnMap(data);
        }
      } else {
        setError('Erreur lors du chargement des clients');
      }
    } catch (err) {
      setError('Erreur de connexion au serveur');
    } finally {
      setLoading(false);
    }
  };

  const loadGoogleMaps = () => {
    if (window.google) {
      setMapLoaded(true);
      initMap();
      return;
    }

    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&callback=initMap`;
    script.async = true;
    script.defer = true;
    
    window.initMap = () => {
      setMapLoaded(true);
      initMap();
    };
    
    document.head.appendChild(script);
  };

  const initMap = () => {
    const map = new window.google.maps.Map(document.getElementById('map'), {
      zoom: 12,
      center: { lat: 33.5731, lng: -7.5898 }, // Casablanca par défaut
      mapTypeId: 'roadmap'
    });
    
    window.currentMap = map;
  };

  const displayClientsOnMap = (clientsData) => {
    if (!window.currentMap || !window.google) return;

    // Effacer les marqueurs existants
    if (window.currentMarkers) {
      window.currentMarkers.forEach(marker => marker.setMap(null));
    }
    window.currentMarkers = [];

    const bounds = new window.google.maps.LatLngBounds();

    clientsData.forEach(client => {
      if (client.posx && client.posy) {
        const position = {
          lat: parseFloat(client.posx),
          lng: parseFloat(client.posy)
        };

        const marker = new window.google.maps.Marker({
          position: position,
          map: window.currentMap,
          title: `${client.nom} ${client.prenom}`,
          icon: {
            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
              <svg width="30" height="40" viewBox="0 0 30 40" xmlns="http://www.w3.org/2000/svg">
                <path d="M15 0C6.7 0 0 6.7 0 15c0 8.3 15 25 15 25s15-16.7 15-25C30 6.7 23.3 0 15 0z" fill="#3b82f6"/>
                <circle cx="15" cy="15" r="8" fill="white"/>
                <text x="15" y="19" text-anchor="middle" font-size="12" fill="#3b82f6">💧</text>
              </svg>
            `),
            scaledSize: new window.google.maps.Size(30, 40)
          }
        });

        const infoWindow = new window.google.maps.InfoWindow({
          content: `
            <div style="padding: 10px;">
              <h3 style="margin: 0 0 10px 0; color: #1f2937;">${client.nom} ${client.prenom}</h3>
              <p style="margin: 5px 0; color: #6b7280;"><strong>Adresse:</strong> ${client.adresse}</p>
              <p style="margin: 5px 0; color: #6b7280;"><strong>Ville:</strong> ${client.ville}</p>
              <p style="margin: 5px 0; color: #6b7280;"><strong>Téléphone:</strong> ${client.tel}</p>
              <div style="margin-top: 15px;">
                <button onclick="window.location.href='/consommation?client=${client.idclient}'" 
                        style="background: #10b981; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-right: 10px;">
                  💧 Nouveau Relevé
                </button>
                <button onclick="window.location.href='/clients/${client.idclient}'" 
                        style="background: #3b82f6; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                  👁️ Voir Détails
                </button>
              </div>
            </div>
          `
        });

        marker.addListener('click', () => {
          infoWindow.open(window.currentMap, marker);
        });

        window.currentMarkers.push(marker);
        bounds.extend(position);
      }
    });

    if (clientsData.length > 0) {
      window.currentMap.fitBounds(bounds);
    }
  };

  const handleSecteurChange = (e) => {
    const secteurId = e.target.value;
    setSelectedSecteur(secteurId);
    if (secteurId) {
      fetchClientsBySecteur(secteurId);
    } else {
      setClients([]);
      if (window.currentMarkers) {
        window.currentMarkers.forEach(marker => marker.setMap(null));
        window.currentMarkers = [];
      }
    }
  };

  const goBack = () => {
    window.history.back();
  };

  return (
    <div className="page-container">
      <header className="page-header">
        <button onClick={goBack} className="back-btn">← Retour</button>
        <h1>🗺️ Carte des Clients</h1>
      </header>

      <main className="page-content">
        <div className="controls-panel">
          <div className="secteur-selector">
            <label htmlFor="secteur">Sélectionner un secteur:</label>
            <select
              id="secteur"
              value={selectedSecteur}
              onChange={handleSecteurChange}
              className="secteur-select"
            >
              <option value="">-- Choisir un secteur --</option>
              {secteurs.map(secteur => (
                <option key={secteur.ids} value={secteur.ids}>
                  {secteur.nom}
                </option>
              ))}
            </select>
          </div>

          {selectedSecteur && (
            <div className="clients-info">
              <span className="clients-count">
                {loading ? 'Chargement...' : `${clients.length} client(s) trouvé(s)`}
              </span>
            </div>
          )}
        </div>

        {error && <div className="error-message">{error}</div>}

        <div className="map-container">
          <div id="map" className="google-map"></div>
          
          {!selectedSecteur && (
            <div className="map-overlay">
              <div className="map-placeholder">
                <div className="placeholder-icon">🗺️</div>
                <h3>Sélectionnez un secteur</h3>
                <p>Choisissez un secteur pour voir les clients sur la carte</p>
              </div>
            </div>
          )}
        </div>

        {clients.length > 0 && (
          <div className="clients-list">
            <h3>Clients dans ce secteur:</h3>
            <div className="clients-grid">
              {clients.map(client => (
                <div key={client.idclient} className="client-item">
                  <span className="client-name">{client.nom} {client.prenom}</span>
                  <span className="client-address">{client.adresse}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </main>

      <style jsx>{`
        .page-container {
          min-height: 100vh;
          background-color: #f8fafc;
          padding: 20px;
        }
        .page-header {
          display: flex;
          align-items: center;
          gap: 20px;
          margin-bottom: 30px;
          background: white;
          padding: 20px;
          border-radius: 12px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .back-btn {
          background: #6366f1;
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 8px;
          cursor: pointer;
        }
        .controls-panel {
          background: white;
          padding: 20px;
          border-radius: 12px;
          margin-bottom: 20px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .secteur-selector label {
          display: block;
          margin-bottom: 8px;
          font-weight: 500;
          color: #374151;
        }
        .secteur-select {
          padding: 10px;
          border: 1px solid #d1d5db;
          border-radius: 8px;
          font-size: 16px;
          min-width: 200px;
        }
        .clients-info {
          color: #6b7280;
          font-weight: 500;
        }
        .map-container {
          position: relative;
          height: 500px;
          background: white;
          border-radius: 12px;
          overflow: hidden;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .google-map {
          width: 100%;
          height: 100%;
        }
        .map-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(255, 255, 255, 0.95);
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .map-placeholder {
          text-align: center;
          color: #6b7280;
        }
        .placeholder-icon {
          font-size: 64px;
          margin-bottom: 20px;
        }
        .clients-list {
          margin-top: 20px;
          background: white;
          padding: 20px;
          border-radius: 12px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .clients-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
          gap: 10px;
          margin-top: 15px;
        }
        .client-item {
          display: flex;
          flex-direction: column;
          padding: 10px;
          background: #f9fafb;
          border-radius: 8px;
          border-left: 4px solid #3b82f6;
        }
        .client-name {
          font-weight: 500;
          color: #1f2937;
        }
        .client-address {
          font-size: 14px;
          color: #6b7280;
        }
        .error-message {
          background: #fee2e2;
          color: #991b1b;
          padding: 15px;
          border-radius: 8px;
          margin-bottom: 20px;
          border: 1px solid #fecaca;
        }
      `}</style>
    </div>
  );
};

export default CarteClients;
