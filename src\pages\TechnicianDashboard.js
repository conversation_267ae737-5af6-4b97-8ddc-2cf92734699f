import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const TechnicianDashboard = ({ route, navigation }) => {
  const user = route?.params?.user;

  const dashboardItems = [
    {
      title: 'Liste des Clients',
      subtitle: 'Consulter mes clients assignés',
      icon: 'people-outline',
      color: '#3b82f6',
      onPress: () => navigation.navigate('ClientsList')
    },
    {
      title: 'Saisie Consommation',
      subtitle: 'Enregistrer les relevés d\'eau',
      icon: 'water-outline',
      color: '#10b981',
      onPress: () => navigation.navigate('Consommation')
    },
    {
      title: 'Consulter Consommations',
      subtitle: 'Voir l\'historique des relevés',
      icon: 'analytics-outline',
      color: '#06b6d4',
      onPress: () => navigation.navigate('ConsommationHistory')
    },
    {
      title: 'Mes Factures',
      subtitle: 'Consulter les factures générées',
      icon: 'document-text-outline',
      color: '#f59e0b',
      onPress: () => navigation.navigate('Factures')
    },
    {
      title: 'Scanner QR Code',
      subtitle: 'Identifier client par QR compteur',
      icon: 'qr-code-outline',
      color: '#8b5cf6',
      onPress: () => navigation.navigate('QRCode')
    },
    {
      title: 'Carte Google Maps',
      subtitle: 'Localiser mes clients',
      icon: 'map-outline',
      color: '#ef4444',
      onPress: () => navigation.navigate('GoogleMaps')
    },
    {
      title: 'Historique Actions',
      subtitle: 'Consulter mes activités',
      icon: 'time-outline',
      color: '#84cc16',
      onPress: () => navigation.navigate('ActionHistory')
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#3b82f6" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.menuButton}
          onPress={() => navigation.openDrawer()}
        >
          <Ionicons name="menu" size={24} color="white" />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Tableau de Bord</Text>
          {user && (
            <Text style={styles.headerSubtitle}>
              Bonjour {user.prenom} {user.nom}
            </Text>
          )}
        </View>
      </View>

      {/* Dashboard Content */}
      <ScrollView style={styles.content}>
        <View style={styles.welcomeCard}>
          <Text style={styles.welcomeTitle}>Bienvenue sur AquaTrack</Text>
          <Text style={styles.welcomeText}>
            Système de gestion de facturation d'eau
          </Text>
        </View>

        <View style={styles.gridContainer}>
          {dashboardItems.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.dashboardCard, { borderLeftColor: item.color }]}
              onPress={item.onPress}
            >
              <View style={styles.cardContent}>
                <View style={[styles.iconContainer, { backgroundColor: item.color + '20' }]}>
                  <Ionicons name={item.icon} size={32} color={item.color} />
                </View>
                <View style={styles.cardText}>
                  <Text style={styles.cardTitle}>{item.title}</Text>
                  <Text style={styles.cardSubtitle}>{item.subtitle}</Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color="#000" />
              </View>
            </TouchableOpacity>
          ))}
        </View>


      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    backgroundColor: '#1369f3ff',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  menuButton: {
    padding: 8,
    marginRight: 12,
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#000',
    marginTop: 2,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  welcomeCard: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 12,
    marginBottom: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
  },
  welcomeText: {
    fontSize: 16,
    color: '#6b7280',
  },
  gridContainer: {
    marginBottom: 24,
  },
  dashboardCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    borderLeftWidth: 4,
  },
  cardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  cardText: {
    flex: 1,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 4,
  },
  cardSubtitle: {
    fontSize: 14,
    color: '#6b7280',
  },

});

export default TechnicianDashboard;
