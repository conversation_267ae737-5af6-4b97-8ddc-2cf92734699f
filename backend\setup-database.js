const { Pool } = require('pg');

// Configuration de la base de données
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation',
  password: '123456',
  port: 5432,
});

async function setupDatabase() {
  try {
    console.log('🔍 Vérification de la base de données...');
    
    // Test de connexion
    const testConnection = await pool.query('SELECT NOW() as current_time');
    console.log('✅ Connexion réussie:', testConnection.rows[0].current_time);
    
    // Vérifier si la table utilisateur existe
    const tableExists = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'utilisateur'
      );
    `);
    
    if (!tableExists.rows[0].exists) {
      console.log('📋 Création de la table utilisateur...');
      
      // Créer la table utilisateur
      await pool.query(`
        CREATE TABLE utilisateur (
          idtech SERIAL PRIMARY KEY,
          nom VARCHAR(100),
          prenom VARCHAR(100),
          adresse VARCHAR(255),
          tel VARCHAR(20),
          email VARCHAR(100) UNIQUE,
          motdepass VARCHAR(100),
          role VARCHAR(10) CHECK (role IN ('Admin', 'Tech')),
          is_protected BOOLEAN DEFAULT FALSE
        );
      `);
      
      console.log('✅ Table utilisateur créée');
    } else {
      console.log('✅ Table utilisateur existe déjà');
    }
    
    // Vérifier la structure de la table
    const tableStructure = await pool.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'utilisateur'
      ORDER BY ordinal_position;
    `);
    
    console.log('🏗️ Structure de la table utilisateur:');
    tableStructure.rows.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
    });
    
    // Insérer l'utilisateur de test
    console.log('👤 Création de l\'utilisateur de test...');
    
    const insertUser = await pool.query(`
      INSERT INTO utilisateur (nom, prenom, adresse, tel, email, motdepass, role, is_protected)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      ON CONFLICT (email) DO UPDATE SET
        nom = EXCLUDED.nom,
        prenom = EXCLUDED.prenom,
        motdepass = EXCLUDED.motdepass,
        role = EXCLUDED.role
      RETURNING idtech, nom, prenom, email, role;
    `, [
      'Technicien',
      'Test',
      '123 Rue de Test',
      '0123456789',
      '<EMAIL>',
      'Tech123',
      'Tech',
      false
    ]);
    
    console.log('✅ Utilisateur de test créé/mis à jour:', insertUser.rows[0]);
    
    // Créer aussi un admin de test
    const insertAdmin = await pool.query(`
      INSERT INTO utilisateur (nom, prenom, adresse, tel, email, motdepass, role, is_protected)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      ON CONFLICT (email) DO UPDATE SET
        nom = EXCLUDED.nom,
        prenom = EXCLUDED.prenom,
        motdepass = EXCLUDED.motdepass,
        role = EXCLUDED.role
      RETURNING idtech, nom, prenom, email, role;
    `, [
      'Admin',
      'Test',
      '456 Rue Admin',
      '0987654321',
      '<EMAIL>',
      'Admin123',
      'Admin',
      false
    ]);
    
    console.log('✅ Utilisateur admin créé/mis à jour:', insertAdmin.rows[0]);
    
    // Lister tous les utilisateurs
    const allUsers = await pool.query('SELECT idtech, nom, prenom, email, role FROM utilisateur');
    console.log('👥 Tous les utilisateurs:');
    allUsers.rows.forEach(user => {
      console.log(`  - ${user.nom} ${user.prenom} (${user.email}) - Role: ${user.role}`);
    });
    
  } catch (error) {
    console.error('❌ Erreur:', error.message);
    console.error('📋 Code d\'erreur:', error.code);
    console.error('📋 Détail:', error.detail);
  } finally {
    await pool.end();
  }
}

setupDatabase();
