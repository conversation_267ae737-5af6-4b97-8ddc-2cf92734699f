const { Pool } = require('pg');

// Configuration de la base de données
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation',
  password: 'admin',
  port: 5432,
});

async function testDatabase() {
  try {
    console.log('🔍 Test de connexion à la base de données...');
    
    // Test 1: Connexion de base
    const testConnection = await pool.query('SELECT NOW() as current_time');
    console.log('✅ Connexion réussie:', testConnection.rows[0]);
    
    // Test 2: Vérifier si la table client existe
    const tableExists = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'client'
      );
    `);
    console.log('📋 Table client existe:', tableExists.rows[0].exists);
    
    // Test 3: Vérifier la structure de la table
    const tableStructure = await pool.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'client'
      ORDER BY ordinal_position;
    `);
    console.log('🏗️ Structure de la table client:');
    tableStructure.rows.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
    });
    
    // Test 4: Compter les enregistrements
    const countResult = await pool.query('SELECT COUNT(*) as total FROM client');
    console.log('📊 Nombre de clients:', countResult.rows[0].total);
    
    // Test 5: Récupérer quelques clients
    const clientsResult = await pool.query('SELECT * FROM client LIMIT 5');
    console.log('👥 Premiers clients:');
    clientsResult.rows.forEach(client => {
      console.log(`  - ${client.nom} ${client.prenom} (ID: ${client.idclient})`);
    });
    
  } catch (error) {
    console.error('❌ Erreur:', error.message);
    console.error('📋 Code d\'erreur:', error.code);
    console.error('📋 Détail:', error.detail);
  } finally {
    await pool.end();
  }
}

testDatabase();
