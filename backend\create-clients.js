const { Pool } = require('pg');

// Configuration de la base de données
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation',
  password: '123456',
  port: 5432,
});

async function createClients() {
  try {
    console.log('👥 Création des clients de test...');
    
    // Vérifier si la table client existe
    const tableExists = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'client'
      );
    `);
    
    if (!tableExists.rows[0].exists) {
      console.log('📋 Création de la table client...');
      
      // Créer la table client
      await pool.query(`
        CREATE TABLE client (
          idclient SERIAL PRIMARY KEY,
          nom VARCHAR(100),
          prenom VARCHAR(100),
          adresse VARCHAR(255),
          ville VARCHAR(100),
          tel VARCHAR(20),
          email VARCHAR(100),
          statut VARCHAR(20)
        );
      `);
      
      console.log('✅ Table client créée');
    } else {
      console.log('✅ Table client existe déjà');
    }
    
    // Vérifier la structure de la table
    const tableStructure = await pool.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'client'
      ORDER BY ordinal_position;
    `);
    
    console.log('🏗️ Structure de la table client:');
    tableStructure.rows.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
    });
    
    // Clients de test à insérer
    const clientsData = [
      {
        nom: 'Dupont',
        prenom: 'Jean',
        adresse: '123 Rue de la Paix',
        ville: 'Paris',
        tel: '0123456789',
        email: '<EMAIL>',
        statut: 'Actif'
      },
      {
        nom: 'Martin',
        prenom: 'Marie',
        adresse: '456 Avenue des Champs',
        ville: 'Lyon',
        tel: '0234567890',
        email: '<EMAIL>',
        statut: 'Actif'
      },
      {
        nom: 'Bernard',
        prenom: 'Pierre',
        adresse: '789 Boulevard Saint-Michel',
        ville: 'Marseille',
        tel: '0345678901',
        email: '<EMAIL>',
        statut: 'Inactif'
      },
      {
        nom: 'Dubois',
        prenom: 'Sophie',
        adresse: '321 Rue Victor Hugo',
        ville: 'Toulouse',
        tel: '0456789012',
        email: '<EMAIL>',
        statut: 'Actif'
      },
      {
        nom: 'Moreau',
        prenom: 'Antoine',
        adresse: '654 Place de la République',
        ville: 'Nice',
        tel: '0567890123',
        email: '<EMAIL>',
        statut: 'Suspendu'
      }
    ];
    
    // Insérer les clients
    for (const client of clientsData) {
      const insertClient = await pool.query(`
        INSERT INTO client (nom, prenom, adresse, ville, tel, email, statut)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        ON CONFLICT (email) DO UPDATE SET
          nom = EXCLUDED.nom,
          prenom = EXCLUDED.prenom,
          adresse = EXCLUDED.adresse,
          ville = EXCLUDED.ville,
          tel = EXCLUDED.tel,
          statut = EXCLUDED.statut
        RETURNING idclient, nom, prenom, ville, statut;
      `, [
        client.nom,
        client.prenom,
        client.adresse,
        client.ville,
        client.tel,
        client.email,
        client.statut
      ]);
      
      console.log('✅ Client créé/mis à jour:', insertClient.rows[0]);
    }
    
    // Lister tous les clients
    const allClients = await pool.query(`
      SELECT idclient, nom, prenom, ville, tel, email, statut 
      FROM client 
      ORDER BY nom, prenom
    `);
    
    console.log(`👥 Total des clients: ${allClients.rows.length}`);
    allClients.rows.forEach(client => {
      console.log(`  - ${client.nom} ${client.prenom} (${client.ville}) - Statut: ${client.statut}`);
    });
    
  } catch (error) {
    console.error('❌ Erreur:', error.message);
    console.error('📋 Code d\'erreur:', error.code);
    console.error('📋 Détail:', error.detail);
  } finally {
    await pool.end();
  }
}

createClients();
