import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  StatusBar,
  ScrollView,
} from 'react-native';
// Icônes remplacées par des émojis pour compatibilité React Web

const Clients = ({ navigation, onLogout }) => {
  const [clients, setClients] = useState([]);
  const [filteredClients, setFilteredClients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchText, setSearchText] = useState('');

  useEffect(() => {
    fetchClients();
  }, []);

  useEffect(() => {
    // Filtrer les clients selon le texte de recherche
    if (searchText.trim() === '') {
      setFilteredClients(clients);
    } else {
      const filtered = clients.filter(client =>
        client.nom.toLowerCase().includes(searchText.toLowerCase()) ||
        client.prenom.toLowerCase().includes(searchText.toLowerCase()) ||
        client.email.toLowerCase().includes(searchText.toLowerCase()) ||
        client.ville.toLowerCase().includes(searchText.toLowerCase())
      );
      setFilteredClients(filtered);
    }
  }, [searchText, clients]);

  const fetchClients = async () => {
    try {
      console.log('🔍 Récupération des clients...');
      const response = await fetch('http://localhost:4000/api/clients');

      if (response.ok) {
        const data = await response.json();
        console.log('📊 Données reçues:', data);

        // Vérifier si la réponse contient des clients
        if (data.success && data.clients) {
          setClients(data.clients);
          setFilteredClients(data.clients);
          console.log('✅ Clients chargés:', data.clients.length);
        } else if (data.length) {
          // Si les données sont directement un tableau
          setClients(data);
          setFilteredClients(data);
          console.log('✅ Clients chargés (format direct):', data.length);
        } else {
          setClients([]);
          setFilteredClients([]);
          console.log('⚠️ Aucun client trouvé');
        }
      } else {
        console.error('❌ Erreur HTTP:', response.status);
        setError('Erreur lors du chargement des clients');
      }
    } catch (err) {
      console.error('❌ Erreur de connexion:', err);
      setError('Erreur de connexion au serveur');
    } finally {
      setLoading(false);
    }
  };

  const handleClientPress = (client) => {
    Alert.alert(
      'Actions Client',
      `${client.nom} ${client.prenom}`,
      [
        { text: 'Voir Détails', onPress: () => console.log('Voir détails:', client.idclient) },
        { text: 'Nouveau Relevé', onPress: () => console.log('Nouveau relevé:', client.idclient) },
        { text: 'Scanner QR', onPress: () => console.log('Scanner QR:', client.idclient) },
        { text: 'Annuler', style: 'cancel' }
      ]
    );
  };

  const renderTableHeader = () => (
    <View style={styles.tableHeader}>
      <Text style={styles.headerCell}>Nom & Prénom</Text>
      <Text style={styles.headerCell}>Ville</Text>
      <Text style={styles.headerCell}>Téléphone</Text>
      <Text style={styles.headerCell}>Email</Text>
      <Text style={styles.headerCell}>Statut</Text>
      <Text style={styles.headerCell}>Actions</Text>
    </View>
  );

  const renderClientRow = ({ item, index }) => (
    <TouchableOpacity
      style={[styles.tableRow, index % 2 === 0 ? styles.evenRow : styles.oddRow]}
      onPress={() => handleClientPress(item)}
    >
      <Text style={styles.tableCell}>{item.nom} {item.prenom}</Text>
      <Text style={styles.tableCell}>{item.ville}</Text>
      <Text style={styles.tableCell}>{item.tel}</Text>
      <Text style={styles.tableCell}>{item.email}</Text>
      <View style={styles.tableCell}>
        {item.statut && (
          <Text style={[styles.statusBadge,
            item.statut === 'Actif' ? styles.statusActive :
            item.statut === 'Inactif' ? styles.statusInactive : styles.statusSuspended
          ]}>
            {item.statut}
          </Text>
        )}
      </View>
      <Text style={styles.actionCell}>👁️</Text>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#6366f1" />
          <Text style={styles.loadingText}>Chargement des clients...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => navigation?.goBack()}>
          <Text style={styles.backIcon}>←</Text>
          <Text style={styles.backText}>Retour</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>👥 Les Clients</Text>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Text style={styles.searchIcon}>🔍</Text>
        <TextInput
          style={styles.searchInput}
          placeholder="Rechercher un client..."
          value={searchText}
          onChangeText={setSearchText}
          placeholderTextColor="#999"
        />
      </View>

      {/* Error Message */}
      {error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>❌ {error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={fetchClients}>
            <Text style={styles.retryText}>Réessayer</Text>
          </TouchableOpacity>
        </View>
      ) : null}

      {/* Clients Table */}
      {filteredClients.length > 0 ? (
        <ScrollView style={styles.tableContainer} showsVerticalScrollIndicator={false}>
          {renderTableHeader()}
          <FlatList
            data={filteredClients}
            renderItem={renderClientRow}
            keyExtractor={(item) => item.idclient.toString()}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />
        </ScrollView>
      ) : (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyIcon}>👥</Text>
          <Text style={styles.emptyText}>
            {searchText ? 'Aucun client trouvé pour cette recherche' : 'Aucun client trouvé dans la base de données'}
          </Text>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    color:'#000',
    padding: 20,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
  },
  backIcon: {
    color: '#000',
    fontSize: 20,
    marginRight: 8,
  },
  backText: {
    color: '#000',
    fontSize: 18, // Augmenté de 16 à 18
    fontWeight: '500',
  },
  headerTitle: {
    fontSize: 26, // Augmenté de 24 à 26
    fontWeight: 'bold',
    color: '#000',
    flex: 1,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 20,
    marginHorizontal: 20, // Marges normales pour le tableau
    backgroundColor: 'white',
    borderRadius: 10,
    paddingHorizontal: 15,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 3,
    elevation: 2,
  },
  searchIcon: {
    fontSize: 16,
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 18, // Taille augmentée pour la recherche
    color: '#1f2937',
  },
  tableContainer: {
    flex: 1,
    marginHorizontal: 20,
    marginTop: 10,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#f8fafc',
    borderBottomWidth: 2,
    borderBottomColor: '#e5e7eb',
    paddingVertical: 15,
    paddingHorizontal: 10,
  },
  headerCell: {
    flex: 1,
   Oui, c'est tout à fait possible ! Je vais modifier la page des clients pour afficher les données dans un tableau au lieu des cartes. Cela sera plus compact et professionnel.

Edited file
Clients.js
src/pages
Edited file
Clients.js
src/pages
Edited file
Clients.js
src/pages
Maintenant, je vais remplacer les styles pour créer un tableau professionnel :

Edited file
Clients.js
src/pages
Edited file
Clients.js
src/pages
Edited file
Clients.js
src/pages
Maintenant, ajustons la barre de recherche pour qu'elle soit plus adaptée au tableau :

Edited file
Clients.js
src/pages
Parfait ! J'ai transformé l'affichage des clients en tableau. Voici les principales modifications apportées :

📊 Transformation en tableau réussie !
🔄 Changements effectués :
Structure du tableau :
En-tête avec colonnes : Nom & Prénom, Ville, Téléphone, Email, Statut, Actions
Lignes alternées (blanc/gris clair) pour une meilleure lisibilité
Bordures et séparateurs pour délimiter les cellules
Amélioration de l'interface :
Barre de recherche élargie pour s'adapter à la largeur du tableau
Badges de statut plus compacts et professionnels
Icône d'action (👁️) pour indiquer les actions disponibles
Optimisation mobile :
ScrollView pour permettre le défilement vertical
Cellules flexibles qui s'adaptent à la taille de l'écran
Texte centré pour une présentation uniforme
🎨 Caractéristiques du tableau :
src/pages
📱 Avantages du format tableau :
Plus d'informations visibles - Tous les détails clients en un coup d'œil
Navigation plus rapide - Scan visuel facilité
Interface professionnelle - Aspect plus formel et organisé
Recherche optimisée - Résultats plus faciles à comparer
Responsive design - S'adapte aux différentes tailles d'écran
🔍 Fonctionnalités conservées :
✅ Recherche en temps réel
✅ Actions au clic sur les lignes
✅ Badges de statut colorés
✅ Gestion des états vides
✅ Indicateur de chargement
Le tableau est maintenant plus compact, professionnel et permet d'afficher plus d'informations simultanément tout en conservant une excellente lisibilité sur mobile !

 fontSize: 16,
    fontWeight: 'bold',
    color: '#374151',
    textAlign: 'center',
  },
  tableRow: {
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
    alignItems: 'center',
  },
  evenRow: {
    backgroundColor: '#ffffff',
  },
  oddRow: {
    backgroundColor: '#f9fafb',
  },
  tableCell: {
    flex: 1,
    fontSize: 14,
    color: '#374151',
    textAlign: 'center',
    paddingHorizontal: 5,
  },
  actionCell: {
    flex: 0.5,
    fontSize: 18,
    textAlign: 'center',
    color: '#6366f1',
  },
  statusBadge: {
    fontSize: 12,
    fontWeight: 'bold',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    textAlign: 'center',
    minWidth: 60,
    overflow: 'hidden',
  },
  statusActive: {
    backgroundColor: '#d1fae5',
    color: '#065f46',
  },
  statusInactive: {
    backgroundColor: '#fee2e2',
    color: '#991b1b',
  },
  statusSuspended: {
    backgroundColor: '#fef3c7',
    color: '#92400e',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#000',
  },
  errorContainer: {
    margin: 20,
    padding: 20,
    backgroundColor: '#fef2f2',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#fecaca',
  },
  errorText: {
    color: '#ef4444',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 10,
  },
  retryButton: {
    backgroundColor: '#6366f1',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    alignSelf: 'center',
  },
  retryText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },

  emptyIcon: {
    fontSize: 64,
    color: '#ccc',
  },
  emptyText: {
    fontSize: 16,
    color: '#000',
    textAlign: 'center',
    marginTop: 20,
  },
});
export default Clients;
