const { Pool } = require('pg');

// Configuration de la base de données
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation',
  password: '123456',
  port: 5432,
});

async function checkTable() {
  try {
    console.log('🔍 Vérification de la structure de la table utilisateur...');
    
    // Vérifier la structure de la table
    const tableStructure = await pool.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_name = 'utilisateur'
      ORDER BY ordinal_position;
    `);
    
    console.log('🏗️ Structure actuelle de la table utilisateur:');
    tableStructure.rows.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable}, default: ${col.column_default})`);
    });
    
    // Vérifier si la colonne is_protected existe
    const hasIsProtected = tableStructure.rows.some(col => col.column_name === 'is_protected');
    
    if (!hasIsProtected) {
      console.log('⚠️ La colonne is_protected n\'existe pas. Ajout de la colonne...');
      
      await pool.query(`
        ALTER TABLE utilisateur 
        ADD COLUMN is_protected BOOLEAN DEFAULT FALSE;
      `);
      
      console.log('✅ Colonne is_protected ajoutée avec succès');
    } else {
      console.log('✅ La colonne is_protected existe déjà');
    }
    
  } catch (error) {
    console.error('❌ Erreur:', error.message);
    console.error('📋 Code d\'erreur:', error.code);
  } finally {
    await pool.end();
  }
}

checkTable();
