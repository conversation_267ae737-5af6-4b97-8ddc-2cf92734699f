const { Pool } = require('pg');

// Configuration de la base de données
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation',
  password: '123456',
  port: 5432,
});

async function createUsers() {
  try {
    console.log('👤 Création des utilisateurs de test...');
    
    // Insérer l'utilisateur technicien
    const insertTech = await pool.query(`
      INSERT INTO utilisateur (nom, prenom, adresse, tel, email, password, role, is_protected)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      ON CONFLICT (email) DO UPDATE SET
        nom = EXCLUDED.nom,
        prenom = EXCLUDED.prenom,
        adresse = EXCLUDED.adresse,
        tel = EXCLUDED.tel,
        password = EXCLUDED.password,
        role = EXCLUDED.role,
        is_protected = EXCLUDED.is_protected
      RETURNING idtech, nom, prenom, adresse, tel, email, role, is_protected;
    `, [
      'Technicien',
      'Test',
      '123 Rue de Test',
      '0123456789',
      '<EMAIL>',
      'Tech123',
      'Tech',
      false
    ]);
    
    console.log('✅ Utilisateur technicien créé/mis à jour:', insertTech.rows[0]);
    
    // Insérer l'utilisateur admin
    const insertAdmin = await pool.query(`
      INSERT INTO utilisateur (nom, prenom, adresse, tel, email, password, role, is_protected)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      ON CONFLICT (email) DO UPDATE SET
        nom = EXCLUDED.nom,
        prenom = EXCLUDED.prenom,
        adresse = EXCLUDED.adresse,
        tel = EXCLUDED.tel,
        password = EXCLUDED.password,
        role = EXCLUDED.role,
        is_protected = EXCLUDED.is_protected
      RETURNING idtech, nom, prenom, adresse, tel, email, role, is_protected;
    `, [
      'Admin',
      'Test',
      '456 Rue Admin',
      '0987654321',
      '<EMAIL>',
      'Admin123',
      'Admin',
      true
    ]);
    
    console.log('✅ Utilisateur admin créé/mis à jour:', insertAdmin.rows[0]);
    
    // Lister tous les utilisateurs
    const allUsers = await pool.query(`
      SELECT idtech, nom, prenom, adresse, tel, email, role, is_protected
      FROM utilisateur
    `);
    console.log('👥 Tous les utilisateurs:');
    allUsers.rows.forEach(user => {
      console.log(`  - ${user.nom} ${user.prenom} (${user.email}) - Role: ${user.role} - Protégé: ${user.is_protected}`);
      console.log(`    Adresse: ${user.adresse}, Tel: ${user.tel}`);
    });
    
  } catch (error) {
    console.error('❌ Erreur:', error.message);
    console.error('📋 Code d\'erreur:', error.code);
    console.error('📋 Détail:', error.detail);
  } finally {
    await pool.end();
  }
}

createUsers();
