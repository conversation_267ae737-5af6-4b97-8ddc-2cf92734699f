const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation',
  password: '123456',
  port: 5432,
});

async function insertClients() {
  try {
    console.log('🔍 Insertion de clients...');
    
    // Insérer des clients directement
    const clients = [
      ['<PERSON><PERSON>', '<PERSON>', '123 Rue de la Paix', 'Paris', '0123456789', '<EMAIL>', 'Actif'],
      ['<PERSON>', '<PERSON>', '456 Avenue des Champs', 'Lyon', '0234567890', '<EMAIL>', 'Actif'],
      ['<PERSON>', '<PERSON>', '789 Boulevard Saint-Michel', 'Marseille', '0345678901', '<EMAIL>', 'Inactif']
    ];
    
    for (const client of clients) {
      try {
        const result = await pool.query(`
          INSERT INTO client (nom, prenom, adresse, ville, tel, email, statut)
          VALUES ($1, $2, $3, $4, $5, $6, $7)
          RETURNING idclient, nom, prenom;
        `, client);
        
        console.log('✅ Client inséré:', result.rows[0]);
      } catch (err) {
        console.log('⚠️ Client existe déjà ou erreur:', client[0], client[1]);
      }
    }
    
    // Compter les clients
    const count = await pool.query('SELECT COUNT(*) as total FROM client');
    console.log('📊 Total clients:', count.rows[0].total);
    
    // Lister les clients
    const allClients = await pool.query('SELECT * FROM client LIMIT 10');
    console.log('👥 Clients:');
    allClients.rows.forEach(client => {
      console.log(`  - ${client.nom} ${client.prenom} (${client.ville})`);
    });
    
  } catch (error) {
    console.error('❌ Erreur:', error.message);
  } finally {
    await pool.end();
  }
}

insertClients();
