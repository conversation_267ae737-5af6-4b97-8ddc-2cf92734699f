const express = require('express');
const { Pool } = require('pg');
const router = express.Router();

// Configuration de la base de données PostgreSQL
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation',
  password: '123456',
  port: 5432,
});

// Route de connexion
router.post('/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    console.log('🔐 Tentative de connexion pour:', email);
    
    // Vérification des données d'entrée
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Email et mot de passe requis'
      });
    }

    // Requête pour trouver l'utilisateur
    const userQuery = `
      SELECT
        idtech,
        nom,
        prenom,
        adresse,
        tel,
        email,
        password,
        role,
        is_protected
      FROM utilisateur
      WHERE email = $1
    `;
    
    const result = await pool.query(userQuery, [email]);
    
    if (result.rows.length === 0) {
      console.log('❌ Utilisateur non trouvé:', email);
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }
    
    const user = result.rows[0];
    
    // Vérification du mot de passe (simple comparaison - en production, utilisez bcrypt)
    if (user.password !== password) {
      console.log('❌ Mot de passe incorrect pour:', email);
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }
    
    console.log('✅ Connexion réussie pour:', email, 'Role:', user.role);
    
    // Connexion réussie
    res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        idtech: user.idtech,
        nom: user.nom,
        prenom: user.prenom,
        adresse: user.adresse,
        tel: user.tel,
        email: user.email,
        role: user.role,
        is_protected: user.is_protected
      }
    });
    
  } catch (error) {
    console.error('❌ Erreur lors de la connexion:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la connexion',
      error: error.message
    });
  }
});

// Route de test pour vérifier les utilisateurs
router.get('/auth/users', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT
        idtech,
        nom,
        prenom,
        adresse,
        tel,
        email,
        role,
        is_protected
      FROM utilisateur
    `);
    res.json({
      success: true,
      users: result.rows
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des utilisateurs:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur',
      error: error.message
    });
  }
});

// Route pour créer un utilisateur de test
router.post('/auth/create-test-user', async (req, res) => {
  try {
    const insertQuery = `
      INSERT INTO utilisateur (nom, prenom, adresse, tel, email, password, role, is_protected)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      ON CONFLICT (email) DO UPDATE SET
        nom = EXCLUDED.nom,
        prenom = EXCLUDED.prenom,
        adresse = EXCLUDED.adresse,
        tel = EXCLUDED.tel,
        password = EXCLUDED.password,
        role = EXCLUDED.role,
        is_protected = EXCLUDED.is_protected
      RETURNING idtech, nom, prenom, adresse, tel, email, role, is_protected
    `;

    const result = await pool.query(insertQuery, [
      'Technicien',
      'Test',
      '123 Rue de Test',
      '0123456789',
      '<EMAIL>',
      'Tech123',
      'Tech',
      false
    ]);
    
    if (result.rows.length > 0) {
      res.json({
        success: true,
        message: 'Utilisateur de test créé',
        user: result.rows[0]
      });
    } else {
      res.json({
        success: true,
        message: 'Utilisateur de test existe déjà'
      });
    }
    
  } catch (error) {
    console.error('❌ Erreur lors de la création de l\'utilisateur de test:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur',
      error: error.message
    });
  }
});

module.exports = router;
