import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import './App.css';
import TechnicianeDashbor from './pages/TechnicianeDashbor';
import DashboardAdmin from './pages/dashboard-Admin';
import Clients from './pages/Clients';
import TestClients from './pages/TestClients';
import Consommation from './pages/Consommation';
import Factures from './pages/Factures';
import QRScanner from './pages/QRScanner';
import CarteClients from './pages/CarteClients';
import Historique from './pages/Historique';

// Composant simple pour la version web
const App = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [user, setUser] = useState(null);

  const API_BASE_URL = 'http://localhost:4000';

  const handleLogin = async (e) => {
    e.preventDefault();
    
    if (!email || !password) {
      alert('Veuillez saisir votre email et mot de passe');
      return;
    }

    setLoading(true);

    try {
      console.log('🔐 Tentative de connexion:', { email });

      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email.trim(),
          password: password
        }),
      });

      const data = await response.json();
      console.log('📥 Réponse du serveur:', data);

      if (data.success) {
        setUser(data.user);
        setIsLoggedIn(true);

        // Redirection selon le rôle
        if (data.user.role === 'Tech') {
          console.log('🔧 Redirection vers TechnicianeDashbor.js');
        } else if (data.user.role === 'Admin') {
          console.log('👨‍💼 Redirection vers dashboard-Admin.js');
        }

        alert(`Bienvenue ${data.user.prenom} ${data.user.nom}!\nRôle: ${data.user.role}`);
      } else {
        alert('Erreur de connexion: ' + (data.message || 'Erreur de connexion'));
      }
    } catch (error) {
      console.error('❌ Erreur de connexion:', error);
      alert('Impossible de se connecter au serveur. Vérifiez votre connexion internet et que le serveur est démarré.');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
    setUser(null);
    setEmail('');
    setPassword('');
  };

  if (isLoggedIn && user) {
    // Redirection selon le rôle de l'utilisateur
    return (
      <Router>
        <Routes>
          {user.role === 'Tech' ? (
            <>
              {/* Routes pour le technicien */}
              <Route path="/" element={<TechnicianeDashbor user={user} onLogout={handleLogout} />} />
              <Route path="/dashboard" element={<TechnicianeDashbor user={user} onLogout={handleLogout} />} />
              <Route path="/clients" element={<Clients user={user} onLogout={handleLogout} />} />
              <Route path="/test-clients" element={<TestClients user={user} onLogout={handleLogout} />} />
              <Route path="/consommation" element={<Consommation user={user} onLogout={handleLogout} />} />
              <Route path="/factures" element={<Factures user={user} onLogout={handleLogout} />} />
              <Route path="/qr-scanner" element={<QRScanner user={user} onLogout={handleLogout} />} />
              <Route path="/carte-clients" element={<CarteClients user={user} onLogout={handleLogout} />} />
              <Route path="/historique" element={<Historique user={user} onLogout={handleLogout} />} />
              <Route path="*" element={<Navigate to="/" replace />} />
            </>
          ) : user.role === 'Admin' ? (
            <>
              {/* Routes pour l'admin */}
              <Route path="/" element={<DashboardAdmin user={user} onLogout={handleLogout} />} />
              <Route path="/dashboard-admin" element={<DashboardAdmin user={user} onLogout={handleLogout} />} />
              <Route path="*" element={<Navigate to="/" replace />} />
            </>
          ) : (
            // Rôle non reconnu
            <Route path="*" element={
              <div className="dashboard">
                <header className="dashboard-header">
                  <h1>❌ Erreur d'Accès</h1>
                  <div className="user-info">
                    <button onClick={handleLogout} className="logout-btn">
                      Retour à la connexion
                    </button>
                  </div>
                </header>
                <main className="dashboard-content">
                  <div className="welcome-card">
                    <h2>Rôle non autorisé</h2>
                    <p>Votre rôle "{user.role}" n'est pas reconnu par le système.</p>
                    <p>Veuillez contacter l'administrateur.</p>
                  </div>
                </main>
              </div>
            } />
          )}
        </Routes>
      </Router>
    );
  }

  return (
    <div className="login-container">
      <div className="login-card">
        <div className="logo">
          <div className="logo-icon">🌊</div>
          <h1>AquaTrack</h1>
          <p>Système de Facturation Mobile</p>
        </div>
        
        <form onSubmit={handleLogin} className="login-form">
          <div className="form-group">
            <label htmlFor="email">Email</label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Entrez votre email"
              disabled={loading}
              required
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="password">Mot de passe</label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Entrez votre mot de passe"
              disabled={loading}
              required
            />
          </div>
          
          <button 
            type="submit" 
            className="login-btn"
            disabled={loading}
          >
            {loading ? 'Connexion...' : 'Se connecter'}
          </button>
        </form>
      </div>
    </div>
  );
};

export default App;
