import React, { useState, useEffect } from 'react';

const Historique = () => {
  const [historique, setHistorique] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filter, setFilter] = useState('all'); // all, consommation, facture, scan
  const [dateFilter, setDateFilter] = useState(''); // today, week, month, all

  useEffect(() => {
    fetchHistorique();
  }, []);

  const fetchHistorique = async () => {
    try {
      const response = await fetch('http://localhost:4000/api/historique/technicien/1'); // ID du technicien connecté
      if (response.ok) {
        const data = await response.json();
        setHistorique(data);
      } else {
        setError('Erreur lors du chargement de l\'historique');
      }
    } catch (err) {
      setError('Erreur de connexion au serveur');
    } finally {
      setLoading(false);
    }
  };

  const getFilteredHistorique = () => {
    let filtered = historique;

    // Filtrer par type d'action
    if (filter !== 'all') {
      filtered = filtered.filter(item => item.type === filter);
    }

    // Filtrer par date
    if (dateFilter) {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      
      filtered = filtered.filter(item => {
        const itemDate = new Date(item.date);
        
        switch (dateFilter) {
          case 'today':
            return itemDate >= today;
          case 'week':
            const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            return itemDate >= weekAgo;
          case 'month':
            const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
            return itemDate >= monthAgo;
          default:
            return true;
        }
      });
    }

    return filtered.sort((a, b) => new Date(b.date) - new Date(a.date));
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Aujourd\'hui';
    if (diffDays === 2) return 'Hier';
    if (diffDays <= 7) return `Il y a ${diffDays - 1} jours`;
    
    return date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getActionIcon = (type) => {
    switch (type) {
      case 'consommation': return '💧';
      case 'facture': return '🧾';
      case 'scan': return '📱';
      case 'login': return '🔐';
      default: return '📋';
    }
  };

  const getActionColor = (type) => {
    switch (type) {
      case 'consommation': return '#10b981';
      case 'facture': return '#f59e0b';
      case 'scan': return '#8b5cf6';
      case 'login': return '#6b7280';
      default: return '#3b82f6';
    }
  };

  const goBack = () => {
    window.history.back();
  };

  if (loading) {
    return (
      <div className="page-container">
        <div className="loading">Chargement de l'historique...</div>
      </div>
    );
  }

  const filteredData = getFilteredHistorique();

  return (
    <div className="page-container">
      <header className="page-header">
        <button onClick={goBack} className="back-btn">← Retour</button>
        <h1>📋 Historique des Actions</h1>
      </header>

      <main className="page-content">
        {error && <div className="error-message">{error}</div>}
        
        <div className="filters-container">
          <div className="filter-group">
            <label>Type d'action:</label>
            <select value={filter} onChange={(e) => setFilter(e.target.value)}>
              <option value="all">Toutes les actions</option>
              <option value="consommation">Relevés de consommation</option>
              <option value="facture">Factures consultées</option>
              <option value="scan">Scans QR</option>
              <option value="login">Connexions</option>
            </select>
          </div>

          <div className="filter-group">
            <label>Période:</label>
            <select value={dateFilter} onChange={(e) => setDateFilter(e.target.value)}>
              <option value="">Toute la période</option>
              <option value="today">Aujourd'hui</option>
              <option value="week">Cette semaine</option>
              <option value="month">Ce mois</option>
            </select>
          </div>
        </div>

        <div className="stats-summary">
          <div className="stat-item">
            <span className="stat-number">{historique.filter(h => h.type === 'consommation').length}</span>
            <span className="stat-label">Relevés</span>
          </div>
          <div className="stat-item">
            <span className="stat-number">{historique.filter(h => h.type === 'scan').length}</span>
            <span className="stat-label">Scans QR</span>
          </div>
          <div className="stat-item">
            <span className="stat-number">{historique.filter(h => h.type === 'facture').length}</span>
            <span className="stat-label">Factures</span>
          </div>
        </div>

        <div className="historique-list">
          {filteredData.length > 0 ? (
            filteredData.map((item, index) => (
              <div key={index} className="historique-item">
                <div 
                  className="action-icon"
                  style={{ backgroundColor: getActionColor(item.type) }}
                >
                  {getActionIcon(item.type)}
                </div>
                
                <div className="action-content">
                  <div className="action-title">{item.description}</div>
                  <div className="action-details">
                    {item.client && <span>Client: {item.client}</span>}
                    {item.montant && <span>Montant: {item.montant} MAD</span>}
                    {item.periode && <span>Période: {item.periode}</span>}
                  </div>
                  <div className="action-time">{formatDate(item.date)}</div>
                </div>

                <div className="action-status">
                  <span className={`status ${item.status || 'completed'}`}>
                    {item.status === 'pending' ? 'En cours' : 'Terminé'}
                  </span>
                </div>
              </div>
            ))
          ) : (
            <div className="no-data">
              <div className="no-data-icon">📋</div>
              <h3>Aucune action trouvée</h3>
              <p>
                {filter !== 'all' || dateFilter 
                  ? 'Aucune action ne correspond aux filtres sélectionnés'
                  : 'Vous n\'avez encore effectué aucune action'
                }
              </p>
            </div>
          )}
        </div>
      </main>

      <style jsx>{`
        .page-container {
          min-height: 100vh;
          background-color: #f8fafc;
          padding: 20px;
        }
        .page-header {
          display: flex;
          align-items: center;
          gap: 20px;
          margin-bottom: 30px;
          background: white;
          padding: 20px;
          border-radius: 12px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .back-btn {
          background: #6366f1;
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 8px;
          cursor: pointer;
        }
        .filters-container {
          display: flex;
          gap: 20px;
          margin-bottom: 30px;
          background: white;
          padding: 20px;
          border-radius: 12px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .filter-group {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }
        .filter-group label {
          font-weight: 500;
          color: #374151;
        }
        .filter-group select {
          padding: 8px 12px;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          font-size: 14px;
        }
        .stats-summary {
          display: flex;
          gap: 20px;
          margin-bottom: 30px;
          justify-content: center;
        }
        .stat-item {
          background: white;
          padding: 20px;
          border-radius: 12px;
          text-align: center;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          min-width: 120px;
        }
        .stat-number {
          display: block;
          font-size: 32px;
          font-weight: bold;
          color: #3b82f6;
          margin-bottom: 8px;
        }
        .stat-label {
          color: #6b7280;
          font-size: 14px;
        }
        .historique-list {
          background: white;
          border-radius: 12px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          overflow: hidden;
        }
        .historique-item {
          display: flex;
          align-items: center;
          padding: 20px;
          border-bottom: 1px solid #f3f4f6;
        }
        .historique-item:last-child {
          border-bottom: none;
        }
        .action-icon {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 20px;
          margin-right: 16px;
        }
        .action-content {
          flex: 1;
        }
        .action-title {
          font-weight: 500;
          color: #1f2937;
          margin-bottom: 4px;
        }
        .action-details {
          font-size: 14px;
          color: #6b7280;
          margin-bottom: 4px;
        }
        .action-details span {
          margin-right: 15px;
        }
        .action-time {
          font-size: 12px;
          color: #9ca3af;
        }
        .action-status {
          margin-left: 16px;
        }
        .status {
          padding: 4px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 500;
        }
        .status.completed {
          background: #d1fae5;
          color: #065f46;
        }
        .status.pending {
          background: #fef3c7;
          color: #92400e;
        }
        .no-data {
          text-align: center;
          padding: 60px 20px;
          color: #6b7280;
        }
        .no-data-icon {
          font-size: 64px;
          margin-bottom: 20px;
        }
        .loading, .error-message {
          text-align: center;
          padding: 40px;
          background: white;
          border-radius: 12px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .error-message {
          color: #ef4444;
          background: #fef2f2;
          border: 1px solid #fecaca;
        }
      `}</style>
    </div>
  );
};

export default Historique;
